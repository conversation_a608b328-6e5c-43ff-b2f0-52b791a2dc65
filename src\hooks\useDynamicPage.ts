import { useState, useCallback, useMemo } from "react";
import { useTable } from "@/hooks/useTable";
import { useToast } from "@/hooks/use-toast";
import { ListPageConfig, PageConfig } from "@/lib/types/page-config";
import { EntityService } from "@/lib/services/entity-service";
import { useAuth } from "@/contexts/AuthContext";
import { useNavigate } from "react-router-dom";

interface UseDynamicPageProps {
  config: PageConfig;
}

interface UseDynamicPageReturn {
  // Common properties
  isLoading: boolean;
  error: Error | null;

  // List page properties (only available if config.type === "list")
  tableData?: any[];
  pageCount?: number;
  totalCount?: number;
  pagination?: any;
  sorting?: any;
  handlePaginationChange?: (pagination: any) => void;
  handleSortingChange?: (sorting: any) => void;
  handleFilterChange?: (filter: any) => void;
  refreshData?: () => void;

  // Action handlers
  handleCreate: () => void;
  handleView: (id: string) => void;
  handleEdit: (id: string) => void;
  handleDelete: (id: string) => Promise<boolean>;

  // Form state
  showForm: boolean;
  setShowForm: (show: boolean) => void;
  currentEntityId: string | null;
  setCurrentEntityId: (id: string | null) => void;

  // Permissions
  hasRequiredPermissions: boolean;
}

/**
 * Custom hook for managing dynamic page state and actions
 */
export function useDynamicPage({
  config,
}: UseDynamicPageProps): UseDynamicPageReturn {
  const { toast } = useToast();
  const { hasPermission } = useAuth();
  const navigate = useNavigate();

  // State for form visibility
  const [showForm, setShowForm] = useState(false);
  const [currentEntityId, setCurrentEntityId] = useState<string | null>(null);

  // Check if user has required permissions - memoize this calculation
  const hasRequiredPermissions = useMemo(
    () =>
      !config.permissions ||
      config.permissions.every((permission) =>
        hasPermission(permission as any)
      ),
    [config.permissions, hasPermission]
  );

  // Create a stable fetchData function for useTable
  const fetchData = useCallback(
    async (pagination: any, sorting: any, filter: any) => {
      return await EntityService.getEntities(
        config.entityName,
        config.endpoints.list,
        pagination,
        sorting,
        filter
      );
    },
    [config.entityName]
  );

  // Initialize table if this is a list page
  const tableHook = useTable({
    fetchData,
    initialPageSize: (config as ListPageConfig).defaultPageSize || 10,
    initialSorting: (config as ListPageConfig).defaultSorting || [],
  });

  // Handle create action
  const handleCreate = useCallback(() => {
    setCurrentEntityId(null);
    setShowForm(true);
  }, []);

  // Handle view action - navigate to details page
  const handleView = useCallback(
    (id: string) => {
      // Navigate to the details page for this entity
      // Use lowercase for URL path but preserve original case for the entity name
      navigate(`/projects/${config.entityName.toLowerCase()}/${id}`);
    },
    [navigate, config.entityName]
  );

  // Handle edit action - show form dialog
  const handleEdit = useCallback((id: string) => {
    setCurrentEntityId(id);
    setShowForm(true);
  }, []);

  // Handle delete action - memoized
  const handleDelete = useCallback(
    async (id: string) => {
      try {
        const success = await EntityService.deleteEntity(
          config.entityName,
          config.endpoints.delete,
          id
        );

        if (success) {
          toast({
            title: "Success",
            description: `${config.entityName} deleted successfully.`,
          });

          // Refresh table data if available
          if (tableHook?.refreshData) {
            tableHook.refreshData();
          }

          return true;
        } else {
          toast({
            title: "Error",
            description: `Failed to delete ${config.entityName}.`,
            variant: "destructive",
          });

          return false;
        }
      } catch (error) {
        console.error(`Error deleting ${config.entityName}:`, error);

        const errorMessage =
          error instanceof Error ? error.message : "Unknown error occurred";
        toast({
          title: "Error",
          description: `Failed to delete ${config.entityName}. ${errorMessage}`,
          variant: "destructive",
        });

        return false;
      }
    },
    [config.entityName, toast, tableHook?.refreshData]
  );

  // Memoize the return object to prevent unnecessary re-renders
  const returnValue = useMemo(
    () => ({
      // Common properties
      isLoading: tableHook?.isLoading || false,
      error: tableHook?.error || null,

      // List page properties
      tableData: tableHook?.data,
      pageCount: tableHook?.pageCount,
      totalCount: tableHook?.totalCount,
      pagination: tableHook?.pagination,
      sorting: tableHook?.sorting,
      handlePaginationChange: tableHook?.handlePaginationChange,
      handleSortingChange: tableHook?.handleSortingChange,
      handleFilterChange: tableHook?.handleFilterChange,
      refreshData: tableHook?.refreshData,

      // Action handlers
      handleCreate,
      handleView,
      handleEdit,
      handleDelete,

      // Form state
      showForm,
      setShowForm,
      currentEntityId,
      setCurrentEntityId,

      // Permissions
      hasRequiredPermissions,
    }),
    [
      tableHook?.isLoading,
      tableHook?.error,
      tableHook?.data,
      tableHook?.pageCount,
      tableHook?.totalCount,
      tableHook?.pagination,
      tableHook?.sorting,
      tableHook?.handlePaginationChange,
      tableHook?.handleSortingChange,
      tableHook?.handleFilterChange,
      tableHook?.refreshData,
      handleCreate,
      handleView,
      handleEdit,
      handleDelete,
      showForm,
      setShowForm,
      currentEntityId,
      setCurrentEntityId,
      hasRequiredPermissions,
    ]
  );

  return returnValue;
}
